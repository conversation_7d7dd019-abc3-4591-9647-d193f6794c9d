<template>
    <div class="customer-management">
        <div class="page-header">
            <h1>客户管理</h1>
            <div class="action-buttons">
                <button class="btn btn-primary" @click="showRegisterForm = !showRegisterForm">{{ showRegisterForm ?
                    (isEditMode
                        ? '取消编辑' : '取消添加') :
                    '添加客户' }}</button>
            </div>
        </div>

        <div class="content-container">
            <!-- 客户表单部分 -->
            <form class="customer-form" v-if="showRegisterForm" @submit.prevent="submitCustomer">
                <h3>{{ isEditMode ? '编辑客户' : '添加客户' }}</h3>
                <div class="form-group">
                    <label>账号:</label>
                    <div class="input-wrapper">
                        <input type="text" v-model="newCustomer.account" placeholder="请输入账号">
                        <span class="required-mark">*</span>
                    </div>
                </div>

                <div class="form-group">
                    <label>密码:</label>
                    <div class="input-wrapper">
                        <input type="text" v-model="newCustomer.password"
                            :placeholder="isEditMode ? '请输入新密码' : '系统生成，请拷贝保存'">
                    </div>
                </div>

                <div class=" form-group">
                    <label>昵称</label>
                    <div class="input-wrapper">
                        <input type="text" v-model="newCustomer.nickname" placeholder="请输入昵称">
                        <span class="required-mark">*</span>
                    </div>
                </div>

                <div class="form-group">
                    <label>微信号:</label>
                    <div class="input-wrapper">
                        <input type="text" v-model="newCustomer.wx" placeholder="请输入微信号">
                        <span class="required-mark">*</span>
                    </div>
                </div>

                <div class="form-group">
                    <label>电话:</label>
                    <div class="input-wrapper">
                        <input type="text" v-model="newCustomer.phone" placeholder="请输入电话号码">
                    </div>
                </div>


                <div class="form-group">
                    <label>注册日期:</label>
                    <input type="date" v-model="newCustomer.start">
                </div>

                <button type="submit" class="btn btn-submit">{{ isEditMode ? '保存修改' : '添加客户' }}</button>
            </form>

            <!-- 客户列表部分 -->
            <div>
                <h2 class="section-title">客户列表</h2>
                <div class="filter-container">
                    <el-input v-model="accountSearchQuery" placeholder="按账号搜索" prefix-icon="Search" clearable
                        @input="handleFilterChange" @clear="handleFilterChange" class="search-input" />
                    <el-select v-model="statusFilter" placeholder="按状态筛选" clearable @change="handleFilterChange">
                        <el-option label="全部" value="all" />
                        <el-option label="在籍" value="true" />
                        <el-option label="销户" value="false" />
                    </el-select>
                </div>
                <el-table :data="currentPageCustomers" style="width: 100%" border stripe v-loading="tableLoading">
                    <el-table-column prop="id" label="ID" width="80" />
                    <el-table-column prop="account" label="账号" width="120" />
                    <el-table-column prop="nickname" label="昵称" width="120" />
                    <el-table-column prop="wx" label="微信" width="120" />
                    <el-table-column label="注册日期" width="120">
                        <template #default="scope">
                            {{ formatDate(scope.row.register_time) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="在籍状态" width="100">
                        <template #default="scope">
                            {{ getStatus(scope.row) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="120">
                        <template #default="scope">
                            <el-button size="small" type="success" @click="editCustomer(scope.row)">编辑</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <div class="pagination-container">
                    <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                        :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
                        :total="totalCustomers" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import axios from '@/router/axios';
import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';

interface Customer {
    id: number | string;
    account: string;
    password?: string;
    nickname: string;
    wx: string;
    phone?: string;
    register_time?: string;
    is_active: boolean;
}

const generatePassword = function () {
    // 定义不同类型的字符集
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '**********';
    const specialChars = '!@#$%^&*()_+-=[]{}|;:\",./<>?';

    // 确保密码包含每种类型的至少一个字符
    let password = '';
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += specialChars[Math.floor(Math.random() * specialChars.length)];

    // 生成剩余的字符
    const allChars = lowercase + uppercase + numbers + specialChars;
    while (password.length < 8) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // 打乱密码顺序
    password = password.split('').sort(() => Math.random() - 0.5).join('');
    return password;
}

const router = useRouter();
const showRegisterForm = ref(false);
const statusFilter = ref('all');
const accountSearchQuery = ref(''); // 添加账号搜索查询
const isEditMode = ref(false);
const editingCustomerId = ref<number | null>(null);
const newCustomer = ref({
    account: '',
    password: generatePassword(),
    nickname: '',
    wx: '',
    phone: '',
    start: new Date().toISOString().split('T')[0],
});

const customers = ref<Customer[]>([]);
const customers_all = ref<Customer[]>([]);
const tableLoading = ref(false);

// 分页相关
const currentPage = ref(1);
const pageSize = ref(20);

// 计算属性：根据筛选条件显示不同的用户列表
const filteredCustomers = computed(() => {
    // 首先按状态筛选
    let filtered = [];
    if (statusFilter.value === 'all') {
        filtered = customers_all.value;
    } else if (statusFilter.value) {
        filtered = customers_all.value.filter(customer => customer.is_active === true);
    } else {
        filtered = customers_all.value.filter(customer => customer.is_active === false);
    }

    // 然后按账号搜索
    if (accountSearchQuery.value) {
        const query = accountSearchQuery.value.toLowerCase();
        filtered = filtered.filter(customer =>
            customer.account.toLowerCase().includes(query) ||
            (customer.nickname && customer.nickname.toLowerCase().includes(query))
        );
    }

    return filtered;
});

// 计算当前页的客户数据
const currentPageCustomers = computed(() => {
    const startIndex = (currentPage.value - 1) * pageSize.value;
    return filteredCustomers.value.slice(startIndex, startIndex + pageSize.value);
});

// 计算总客户数
const totalCustomers = computed(() => {
    return filteredCustomers.value.length;
});

// 处理筛选变化
const handleFilterChange = () => {
    currentPage.value = 1; // 重置到第一页

    // 如果有搜索查询，使用后端搜索
    if (accountSearchQuery.value && accountSearchQuery.value.length >= 2) {
        fetchcustomers(accountSearchQuery.value);
    } else if (!accountSearchQuery.value) {
        // 如果搜索查询为空，重新获取所有数据
        fetchcustomers();
    }
};

// 处理页码变化
const handleCurrentChange = (newPage: number) => {
    currentPage.value = newPage;
};

// 处理每页显示数量变化
const handleSizeChange = (newSize: number) => {
    pageSize.value = newSize;
    // 当每页条数变化时，可能需要调整页码
    if (currentPage.value > Math.ceil(totalCustomers.value / pageSize.value)) {
        currentPage.value = 1;
    }
};

// 获取用户状态
const getStatus = (customer: Customer) => {
    if (customer.is_active) {
        return '在籍';
    } else {
        return '销户';
    }
};

// 格式化日期
// 修改formatDate函数
const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
};



// 添加用户
const addCustomer = async () => {
    try {
        const response = await axios.post('/api/admin/customers', newCustomer.value);
        // 更新本地数据
        await fetchcustomers();

        newCustomer.value = {
            account: '',
            password: generatePassword(),
            nickname: '',
            wx: '',
            phone: '',
            start: new Date().toISOString().split('T')[0]
        };
        showRegisterForm.value = false; // 添加后隐藏表单
        ElMessage.success('客户添加成功');
    } catch (error: unknown) {
        // 添加类型断言以处理error对象
        if (error && typeof error === 'object' && 'response' in error &&
            error.response && typeof error.response === 'object' &&
            'status' in error.response && 'data' in error.response) {

            const response = error.response as any;

            // 处理409冲突错误（用户已存在，返回用户信息）
            if (response.status === 409 && Array.isArray(response.data)) {
                const customer = response.data[0] as Customer;
                ElMessage.error(`用户已存在: ${customer.account} (${customer.nickname})`);
                return;
            }

            // 处理400错误（验证失败，返回错误信息）
            if (response.status === 400 && response.data && typeof response.data === 'object' && 'error' in response.data) {
                ElMessage.error(`注册失败: ${response.data.error}`);
                return;
            }

            // 处理其他HTTP错误
            if (response.data && typeof response.data === 'object' && 'error' in response.data) {
                ElMessage.error(`操作失败: ${response.data.error}`);
                return;
            }
        }

        console.error('注册客户时发生错误:', error);
        ElMessage.error('注册失败，请稍后重试');
    }
};

// 更新用户
const updateCustomer = async () => {
    if (!editingCustomerId.value) return;

    try {
        tableLoading.value = true;
        await axios.put(`/api/admin/customers/${editingCustomerId.value}`, {
            account: newCustomer.value.account,
            password: newCustomer.value.password,
            nickname: newCustomer.value.nickname,
            wx: newCustomer.value.wx,
            phone: newCustomer.value.phone
        });

        // 更新本地数据
        await fetchcustomers();

        // 重置表单
        resetForm();
        showRegisterForm.value = false;
        isEditMode.value = false;
        editingCustomerId.value = null;
        ElMessage.success('客户更新成功');
    } catch (error) {
        console.error('更新客户失败:', error);
        ElMessage.error('更新客户失败，请稍后重试');
    } finally {
        tableLoading.value = false;
    }
};

// 表单提交处理
const submitCustomer = () => {
    if (isEditMode.value) {
        updateCustomer();
    } else {
        addCustomer();
    }
};

// 编辑客户
const editCustomer = (customer: Customer) => {
    // 设置编辑模式
    isEditMode.value = true;
    editingCustomerId.value = Number(customer.id);

    // 填充表单数据
    newCustomer.value = {
        account: customer.account,
        password: '', // 出于安全考虑，不填充密码
        nickname: customer.nickname,
        wx: customer.wx,
        phone: customer.phone || '',
        start: customer.register_time ? new Date(customer.register_time).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
    };

    // 显示表单
    showRegisterForm.value = true;
};

// 重置表单
const resetForm = () => {
    newCustomer.value = {
        account: '',
        password: '',
        nickname: '',
        wx: '',
        phone: '',
        start: new Date().toISOString().split('T')[0],
    };
};

// 删除用户
const deleteUser = async (id: number) => {
    try {
        await axios.delete(`/api/admin/customers/${id}`);
        customers.value = customers.value.filter(customer => customer.id !== id);
        customers_all.value = customers_all.value.filter(customer => customer.id !== id);
    } catch (error: unknown) {
        console.error('删除客户失败:', error);
        ElMessage.error('删除客户失败，请稍后重试');
    }
};

// 注销课程
const unenrollCourse = async (id: number, course_id: number) => {
    try {
        await axios.post(`/api/admin/customers/${id}/unenroll`, {
            "course_id": course_id
        });
        ElMessage.success('课程已注销');
        await fetchcustomers(); // 刷新用户列表
    } catch (error: unknown) {
        console.error('注销课程失败:', error);
        ElMessage.error('注销课程失败，请稍后重试');
    }
};

// 登出
const logout = () => {
    localStorage.removeItem('token');
    router.push('/');
};

// 获取用户列表
const fetchcustomers = async (searchQuery?: string) => {
    try {
        const params: any = {};
        if (searchQuery) {
            params.search = searchQuery;
            params.limit = 1000; // 搜索时限制结果数量
        }

        const response = await axios.get('/api/admin/customers', { params });
        customers.value = response.data.filter((customer: Customer) => customer.is_active === true);
        customers_all.value = response.data;
    } catch (error: unknown) {
        console.error('获取客户列表失败:', error);
        ElMessage.error('获取客户列表失败，请稍后重试');
    }
};

// 组件挂载时获取用户列表
onMounted(() => {
    fetchcustomers();
});
</script>

<style scoped>
/* 保持原有样式不变 */
.customer-management {
    padding: 0 20px 30px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eaeaea;
}

h1 {
    margin: 0;
    color: #37474f;
    font-size: 1.8rem;
    font-weight: 500;
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    font-size: 0.9rem;
}

.btn-primary {
    background-color: #1976d2;
    color: white;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid #cfd8dc;
    color: #455a64;
}

.content-container {
    background-color: white;
    border-radius: 8px;
    padding: 30px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.customer-form {
    max-width: 600px;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

label {
    width: 80px;
    text-align: right;
    margin-right: 15px;
    color: #455a64;
    font-weight: 500;
}

input {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid #cfd8dc;
    border-radius: 4px;
    font-size: 0.95rem;
}

.input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.required-mark {
    color: #e53935;
    margin-left: 8px;
    font-weight: bold;
}

.section-title {
    margin: 30px 0 15px;
    color: #37474f;
    font-size: 1.4rem;
    font-weight: 500;
}

.customer-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.customer-table th,
.customer-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eaeaea;
}

.customer-table th {
    background-color: #f5f7fa;
    color: #455a64;
    font-weight: 500;
}

.customer-table tr:hover {
    background-color: #f9fafb;
}

.btn-view,
.btn-edit {
    padding: 5px 10px;
    margin-right: 5px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.85rem;
}

.btn-view {
    background-color: #4CAF50;
    color: white;
}

.btn-edit {
    background-color: #2196F3;
    color: white;
}

.btn-courses {
    background-color: #1976d2;
    color: white;
    text-decoration: none;
    display: inline-block;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.85rem;
}

.btn-courses:hover {
    background-color: #1565c0;
}

/* 分页容器样式 */
.pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

.filter-container {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-end;
}

.filter-container .el-select {
    width: 180px;
}

.search-input {
    width: 220px;
    margin-right: 10px;
}
</style>
