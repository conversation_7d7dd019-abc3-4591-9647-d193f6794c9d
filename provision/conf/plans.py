import datetime
import re
from collections import defaultdict
from pathlib import Path

import yaml
from loguru import logger
from pydantic import BaseModel
from typing_extensions import deprecated

from provision.common.helper import get_meta_from_notebook
from provision.conf import cfg


class PlanModel(BaseModel):  # type: ignore
    plans: list[str]
    own_container: list[str]
    retention: int = 30
    refund: int = 15
    initial: int = 3
    release_every: float = 2
    exclude: dict[str, list[str]] = {}
    prime: dict[str, int] = {}
    grace: dict[str, int] = {}


class ServicePlan:
    def __init__(self):
        """
        初始化 ServicePlan 类，加载 plans.yaml 配置文件。

        从配置目录中读取 plans.yaml 文件，并将其内容加载到字典中。
        """
        # planmodels
        self.plans = {}

        # 每个 course 下，所有资源的列表: {course}/{sub} => list[rel_path]
        # 在访问鉴权时，只有相对路径在此集合中的，才需要进行鉴权
        self.controlled_resources = defaultdict(list[str])

        # 每个 course 的每个套餐，排除了 exclude 之后的资源
        # {course.plan} => list[rel_path]
        self.plan_resources = defaultdict(list[str])

    def load_config(self):
        """解析plans.yaml"""
        config_file = cfg.get_config_dir() / "plans.yaml"
        try:
            with open(config_file, "r", encoding="utf-8") as f:
                data = yaml.safe_load(f)

            # 处理每个course
            for course, v in data.items():
                if course not in cfg.resources.model_fields_set:
                    continue
                try:
                    self.plans[course] = PlanModel(**v)
                except Exception as e:  # type: ignore
                    logger.error(f"Failed to load plan for {course}: {e}")

            self.find_controlled_resources()
            self.calc_plan_resources()
        except Exception as e:  # type: ignore
            logger.error(f"Failed to load plans: {e}")

    def get_retention_period(self, course: str):
        plan_model = self.plans.get(course)
        if not plan_model:
            return 30

        return plan_model.retention

    def get_refund_period(self, course: str):
        plan_model = self.plans.get(course)
        if not plan_model:
            return 30

        return plan_model.refund

    def get_prime_period(self, course: str, plan: str):
        plan_model = self.plans.get(course)
        if not plan_model:
            return 60

        return plan_model.prime.get(plan, 60)

    def get_grace_period(self, course: str, plan: str):
        plan_model = self.plans.get(course)
        if not plan_model:
            return 305

        return plan_model.grace.get(plan, 305)

    def iter_course_disk_resources(self):
        """以iterate的方式返回课程下的受控资源，已在内部排序"""
        for course, _ in self.plans.items():
            home = cfg.get_course_home(course)
            assert home
            # for resource in ("courseware", "assignments")
            for resource in cfg.get_course_resources(course):
                notebooks = (home / resource).glob("**/*.ipynb")
                notebooks = self.sort_disk_resources(course, list(notebooks))
                yield course, resource, [str(nb.relative_to(home)) for nb in notebooks]

    def sort_disk_resources(self, course: str, resources: list[Path]) -> list[Path]:
        """对资源列表进行排序"""
        # 如果是课程，按文件名中的数字序号排序
        division = cfg.get_course_division(course)
        if division == "course":

            def extract_digits(path):
                match = re.search(r"(\d+)", path.stem)
                if match:
                    return int(match.group(1))
                return float("inf")

            resources.sort(key=extract_digits)
        elif division == "blog":
            items = {}
            for path in resources:
                *_, publish_date = get_meta_from_notebook(path)
                items[path] = publish_date

            resources = sorted(items, key=lambda k: items[k])
        else:
            raise ValueError(f"Unknown division {division} in resources.yaml")
        return resources

    def find_controlled_resources(self) -> None:
        """解析plans.yaml，得到每门课程的受控资源列表（与用户无关）"""
        for course, plan_model in self.plans.items():
            course_home = cfg.get_course_home(course)
            if course_home is None:
                logger.error(f"Home path not found for course {course}")
                continue
            for sub in cfg.get_course_resources(course):
                plan_model = self.plans.get(course)
                if not plan_model:
                    logger.error(f"Plan not found for course {course}")
                    continue

                resource_path = course_home / sub
                if not resource_path.exists():
                    logger.warning(f"配置文件错误： {resource_path} does not exist")
                    continue

                notebooks = list(resource_path.glob("**/*.ipynb"))
                rel_paths = [str(nb.relative_to(course_home)) for nb in notebooks]
                key = f"{course}.{sub}"
                self.controlled_resources[key].extend(rel_paths)

    def filter_exclude(self, excludes: list[str], notebooks: list[str]) -> list[str]:
        """从所有资源中，排除套餐不能访问的资源"""
        availables = []

        for notebook in notebooks:
            excluded = False
            for exclude in excludes:
                if notebook.startswith(exclude):
                    excluded = True
                    break

            if not excluded:
                availables.append(notebook)

        return availables

    def calc_plan_resources(self):
        """计算每个课程，每个套餐可以访问的resources -- 排除低级套餐不能访问的"""
        for course, model in self.plans.items():
            for plan_name in model.plans:
                for sub in cfg.get_course_resources(course):
                    key = f"{course}.{sub}.{plan_name}"
                    self.plan_resources[key] = self.filter_exclude(
                        model.exclude.get(plan_name, []),
                        self.controlled_resources[f"{course}.{sub}"],
                    )

    def has_own_container(self, course: str, plan: str) -> bool:
        """Check if a course and plan has its own container"""
        # Use plans instead of d
        plan_model = self.plans.get(course)
        if not plan_model:
            return False

        return plan in plan_model.own_container

    def get_exclude(self, course: str, plan: str, resource_type: str) -> list[str]:
        """获取排除列表"""
        plan_model = self.plans.get(course)
        if not plan_model:
            return []

        excludes = plan_model.exclude.get(plan, [])
        result = []
        for exclude in excludes:
            if exclude.startswith(resource_type):
                result.append(exclude)
        return result

    def is_exclude(
        self, course: str, plan: str, resource_type: str, rel_path: str
    ) -> bool:
        """判断受控资源是否在套餐的排除列表中。

        在套餐排除列表中的资源，除非升级套餐，都不可访问。
        """
        plan_model = self.plans.get(course)
        if plan_model is None:
            return False

        for exclude in self.get_exclude(course, plan, resource_type):
            if rel_path.startswith(exclude):
                return True

        return False

    def get_division(self, course: str) -> str:
        """资源的分类。

        如果资源是课程型的，那么显示时，应该以数字为显示名，按数字顺序排列；如果是blog型的，则显示文件名，以时间顺序倒序排列。
        """
        model = self.plans.get(course)
        if model is None:
            raise ValueError(f"Course {course} not found in plans")
        return model.division

    @deprecated("可能应该放在api/resources.py")
    def label_resources(
        self,
        course: str,
        plan: str,
        start: datetime.datetime,
        refund_end: datetime.datetime,
        now: datetime.datetime,
    ) -> dict[str, list[tuple[str, str | None, str | None]]]:
        """根据course, plan, start, refund_end, now对所有资源进行标记

        返回结果为 [(title, rel_path, reason)], title为链接名（显示）， rel_path为相对路径， reason为标记原因。如果不能访问，则rel_path为空，reason是原因；如果能访问，则reason为空。

        返回结果必须按资源的顺序返回。
        """
        labelled = defaultdict(list)

        plan_model = self.plans.get(course)
        assert plan_model
        initial = plan_model.initial
        release_every = plan_model.release_every

        for key, sub_resources in self.controlled_resources.items():
            sub = key.split(".")[1]
            excluded = self.filter_exclude(
                self.get_exclude(course, plan, sub),
                sub_resources,
            )

            available_count = len(excluded)
            if now < refund_end:
                # 在退款期内， 根据公式计算可访问资源数量
                time_diff = (now - start).total_seconds() / 86400
                available_count = min(
                    initial + int(time_diff / release_every),
                    len(sub_resources),
                )

            i = 0
            for rel_path in sub_resources:
                if self.is_exclude(course, plan, sub, rel_path):
                    labelled[key].append((rel_path, "升级套餐，立刻解锁"))
                elif now < refund_end and i < available_count:
                    labelled[key].append((rel_path, None))
                elif now < refund_end and i >= available_count:
                    labelled[key].append((None, "升级套餐，请稍后解锁"))
                else:  # now > refund_end
                    labelled[key].append((rel_path, None))
            # 可以访问的资源， reason = None
            if sub in ("courseware", "assignments"):
                # this is hack
                labelled[sub].extend(
                    [
                        (f"{i:02d}", rel_path, None)
                        for i, rel_path in enumerate(sub_resources[:available_count])
                    ]
                )
            else:
                labelled[sub].extend(
                    [(rel_path, None) for rel_path in sub_resources[:available_count]]
                )

            # 找到套餐排除的资源
            # 找到其它不可访问的资源
        return labelled
    @classmethod
    def get_plans_for_course(cls, course: str) -> list[str]:
        """获取课程的所有可用套餐

        Args:
            course: 课程ID

        Returns:
            list[str]: 套餐列表
        """
        plan_model = sp.plans.get(course)
        if plan_model is None:
            return []

        return plan_model.plans


sp = ServicePlan()

__all__ = ["sp"]
