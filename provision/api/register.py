"""
API endpoints for customer registration and SMS verification.
"""

import hashlib
import re
from typing import Dict, Optional

import jwt
from blacksheep import Request, json
from loguru import logger
from pydantic import BaseModel, Field, field_validator

from provision.common.captcha import generate_captcha, verify_captcha
from provision.common.router import router
from provision.common.sessions import sessions
from provision.common.sms import send_verification_code, verify_code
from provision.conf import cfg
from provision.store.customer import CustomerModel
from provision.store.db import pg


class CaptchaRequest(BaseModel):
    """Request model for CAPTCHA generation."""

    pass


class CaptchaVerificationRequest(BaseModel):
    """Request model for CAPTCHA verification."""

    captcha_id: str
    captcha_code: str


class VerificationRequest(BaseModel):
    """Request model for SMS verification."""

    phone: str
    captcha_id: str
    captcha_code: str

    @field_validator("phone")
    def validate_phone(cls, value):
        """Validate phone number format."""
        if not re.match(r"^\d{11}$", value):
            raise ValueError("Phone number must be 11 digits")
        return value


class VerificationCodeRequest(BaseModel):
    """Request model for verification code validation."""

    phone: str
    code: str

    @field_validator("phone")
    def validate_phone(cls, value):
        """Validate phone number format."""
        if not re.match(r"^\d{11}$", value):
            raise ValueError("Phone number must be 11 digits")
        return value

    @field_validator("code")
    def validate_code(cls, value):
        """Validate verification code format."""
        if not re.match(r"^\d{6}$", value):
            raise ValueError("Verification code must be 6 digits")
        return value


class RegistrationRequest(BaseModel):
    """Request model for customer registration."""

    account: str
    password: str
    confirm_password: str
    nickname: str
    phone: str
    wx: str
    verification_code: str

    @field_validator("account")
    def validate_account(cls, value):
        """Validate account format."""
        if not re.match(r"^[a-zA-Z0-9]{4,20}$", value):
            raise ValueError(
                "用户名必须是4-20个字符，只能包含字母和数字"
            )
        return value

    @field_validator("password")
    def validate_password(cls, value):
        """Validate password strength."""
        if len(value) < 8:
            raise ValueError("密码至少需要8个字符")

        # Check for at least one uppercase, one lowercase, one digit, and one special character
        if not re.search(r"[A-Z]", value):
            raise ValueError("密码必须包含至少一个大写字母")
        if not re.search(r"[a-z]", value):
            raise ValueError("密码必须包含至少一个小写字母")
        if not re.search(r"\d", value):
            raise ValueError("密码必须包含至少一个数字")
        if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", value):
            raise ValueError("密码必须包含至少一个特殊字符")

        return value

    @field_validator("confirm_password")
    def validate_confirm_password(cls, value, values):
        """Validate that confirm_password matches password."""
        if "password" in values.data and value != values.data["password"]:
            raise ValueError("两次输入的密码不一致")
        return value

    @field_validator("phone")
    def validate_phone(cls, value):
        """Validate phone number format."""
        if not re.match(r"^\d{11}$", value):
            raise ValueError("手机号必须是11位数字")
        return value


@router.post("/sso/customer/get_captcha")
async def get_captcha(request: Request):
    """
    Generate a CAPTCHA for human verification.
    """
    try:
        # Generate a new CAPTCHA
        captcha_id, captcha_code, captcha_image = generate_captcha()

        # For debugging only - remove in production
        logger.debug(f"Generated CAPTCHA: {captcha_id}, code: {captcha_code}")

        return json(
            {
                "status": "success",
                "captcha_id": captcha_id,
                "captcha_image": captcha_image,
            }
        )

    except Exception as e:
        logger.error(f"Error generating CAPTCHA: {e}")
        return json({"error": "Failed to generate CAPTCHA"}, status=500)


@router.post("/sso/customer/verify_captcha")
async def verify_captcha_endpoint(request: Request):
    """
    Verify the CAPTCHA code provided by the user.
    """
    try:
        data = await request.json()
        captcha_request = CaptchaVerificationRequest(**data)

        # Verify the CAPTCHA but don't remove it from cache
        is_valid = verify_captcha(
            captcha_request.captcha_id,
            captcha_request.captcha_code,
            remove_if_valid=False,
        )

        if is_valid:
            return json(
                {"status": "success", "message": "CAPTCHA verification successful"}
            )
        else:
            return json({"error": "Invalid or expired CAPTCHA"}, status=400)

    except ValueError as e:
        logger.error(f"Validation error in verify_captcha: {e}")
        return json({"error": str(e)}, status=400)
    except Exception as e:
        logger.error(f"Error in verify_captcha: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.post("/sso/customer/send_verification")
async def send_sms_verification(request: Request):
    """
    Send SMS verification code to the provided phone number.
    """
    try:
        data = await request.json()
        verification_request = VerificationRequest(**data)

        # First verify the CAPTCHA and remove it from cache if valid
        is_valid = verify_captcha(
            verification_request.captcha_id,
            verification_request.captcha_code,
            remove_if_valid=True,  # Now we can remove it as this is the final use
        )

        if not is_valid:
            return json({"error": "验证码无效或已过期"}, status=400)

        # Check if phone number already exists
        sql = "SELECT COUNT(*) FROM customers WHERE phone = %s"
        duplicate_phone = pg.fetch_item(sql, "count", [verification_request.phone]) or 0

        if duplicate_phone > 0:
            return json({"error": "手机号已被注册"}, status=400)

        # Send verification code
        success, message = send_verification_code(verification_request.phone)

        if success:
            return json({"status": "success", "message": message})
        else:
            return json({"error": message}, status=500)

    except ValueError as e:
        logger.error(f"Validation error in send_verification: {e}")
        return json({"error": str(e)}, status=400)
    except Exception as e:
        logger.error(f"Error in send_verification: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.post("/sso/customer/verify_code")
async def verify_sms_code(request: Request):
    """
    Verify the SMS code provided by the user.
    """
    try:
        data = await request.json()
        verification_request = VerificationCodeRequest(**data)

        # Verify the code but don't remove it from cache
        is_valid = verify_code(
            verification_request.phone, verification_request.code, remove_if_valid=False
        )

        if is_valid:
            return json({"status": "success", "message": "Verification code is valid"})
        else:
            return json({"error": "Invalid or expired verification code"}, status=400)

    except ValueError as e:
        logger.error(f"Validation error in verify_code: {e}")
        return json({"error": str(e)}, status=400)
    except Exception as e:
        logger.error(f"Error in verify_code: {e}")
        return json({"error": "Internal server error"}, status=500)


@router.post("/sso/customer/register")
async def register_customer(request: Request):
    """
    Register a new customer with verification.
    """
    try:
        data = await request.json()
        registration_request = RegistrationRequest(**data)

        # Verify the code and remove it from cache if valid (final use)
        is_valid = verify_code(
            registration_request.phone,
            registration_request.verification_code,
            remove_if_valid=True,  # Now we can remove it as this is the final use
        )

        if not is_valid:
            return json({"error": "验证码无效或已过期"}, status=400)

        # Create customer model
        customer = CustomerModel(
            account=registration_request.account,
            password=registration_request.password,
            nickname=registration_request.nickname,
            phone=registration_request.phone,
            wx=registration_request.wx,
        )

        # Hash the password
        if customer.password:
            hashed_password, salt = CustomerModel.hash_password(customer.password)
            customer.password = hashed_password
            customer.salt = salt
        else:
            return json({"error": "密码不能为空"}, status=400)

        # Check if account already exists
        sql = "SELECT COUNT(*) FROM customers WHERE account = %s"
        account_check = pg.fetch_item(sql, "count", [customer.account]) or 0

        if account_check > 0:
            return json({"error": "用户名已存在"}, status=400)

        # Check if phone already exists
        sql = "SELECT COUNT(*) FROM customers WHERE phone = %s"
        phone_check = pg.fetch_item(sql, "count", [customer.phone]) or 0

        if phone_check > 0:
            return json({"error": "手机号已被注册"}, status=400)

        # Insert customer
        customer.insert_customer()

        # Add customer to sessions
        sessions.add(customer.id)

        # Create JWT token
        token_data = {"account": customer.account, "account_id": customer.id}
        token = jwt.encode(token_data, cfg.provision.server.secret, algorithm="HS256")

        # Create response with show_lottery flag
        response = json(
            {
                "status": "success",
                "message": "Registration successful",
                "redirectUrl": "/academy",
                "customer": customer.id,
                "show_lottery": True,  # 添加标志，表示需要显示抽奖对话框
            }
        )

        # Set cookie
        cookie_value = (
            f"qsession-token={token}; Path=/; Max-Age={3600 * 24 * 30}; HttpOnly"
        )
        response.headers.add(b"Set-Cookie", cookie_value.encode("utf-8"))

        logger.info(f"New customer registered: {customer.account}")
        return response

    except ValueError as e:
        logger.error(f"Validation error in register_customer: {e}")
        return json({"error": str(e)}, status=400)
    except Exception as e:
        logger.error(f"Error in register_customer: {e}")
        return json({"error": "Internal server error"}, status=500)
